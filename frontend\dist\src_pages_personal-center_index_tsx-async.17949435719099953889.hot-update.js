globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/PersonalInfo.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _UnifiedSettingsModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UnifiedSettingsModal.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            /**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */ const PersonalInfo = ()=>{
                _s();
                /**
   * 用户详细信息状态管理
   */ const [userInfo, setUserInfo] = (0, _react.useState)({
                    name: '',
                    position: '',
                    email: '',
                    telephone: '',
                    registerDate: '',
                    lastLoginTime: '',
                    lastLoginTeam: '',
                    teamCount: 0,
                    avatar: ''
                });
                const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
                const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
                // 数据概览状态管理
                const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // Modal状态管理
                const [settingsModalVisible, setSettingsModalVisible] = (0, _react.useState)(false);
                // 获取用户数据和统计数据
                (0, _react.useEffect)(()=>{
                    const fetchUserData = async ()=>{
                        try {
                            const userDetail = await _user.UserService.getUserProfileDetail();
                            setUserInfo(userDetail);
                            setUserInfoError(null);
                        } catch (error) {
                            console.error('获取用户详细信息失败:', error);
                            setUserInfoError('获取用户详细信息失败，请稍后重试');
                        } finally{
                            setUserInfoLoading(false);
                        }
                    };
                    const fetchStatsData = async ()=>{
                        try {
                            const stats = await _user.UserService.getUserPersonalStats();
                            setPersonalStats(stats);
                            setStatsError(null);
                        } catch (error) {
                            console.error('获取统计数据失败:', error);
                            setStatsError('获取统计数据失败，请稍后重试');
                        } finally{
                            setStatsLoading(false);
                        }
                    };
                    fetchUserData();
                    fetchStatsData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: "个人信息",
                    extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        size: "small",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 111,
                            columnNumber: 17
                        }, void 0),
                        onClick: ()=>setSettingsModalVisible(true),
                        style: {
                            borderRadius: 6,
                            width: 28,
                            height: 28,
                            padding: 0,
                            border: '1px solid #d9d9d9',
                            color: '#595959',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                        lineNumber: 109,
                        columnNumber: 9
                    }, void 0),
                    style: {
                        marginBottom: 16,
                        borderRadius: 8
                    },
                    children: [
                        userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "个人信息加载失败",
                            description: userInfoError,
                            type: "error",
                            showIcon: true
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 141,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: userInfoLoading || statsLoading,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                gutter: [
                                    24,
                                    16
                                ],
                                align: "top",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        xs: 24,
                                        sm: 24,
                                        md: 14,
                                        lg: 14,
                                        xl: 14,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                height: '100%',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                justifyContent: 'space-between',
                                                minHeight: '120px',
                                                paddingRight: '8px'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginBottom: '12px'
                                                    },
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 4,
                                                        style: {
                                                            margin: 0,
                                                            fontSize: 20,
                                                            fontWeight: 600,
                                                            color: '#262626',
                                                            lineHeight: 1.2
                                                        },
                                                        children: userInfo.name || '加载中...'
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 163,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 162,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginBottom: '12px',
                                                        flex: 1
                                                    },
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                        size: 16,
                                                        wrap: true,
                                                        direction: "vertical",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 16,
                                                                wrap: true,
                                                                children: [
                                                                    userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                        size: 6,
                                                                        align: "center",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                                style: {
                                                                                    fontSize: 14,
                                                                                    color: '#1890ff'
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 183,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                style: {
                                                                                    color: '#595959',
                                                                                    fontSize: 14,
                                                                                    fontWeight: 500
                                                                                },
                                                                                children: userInfo.email
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 189,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 182,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                        size: 6,
                                                                        align: "center",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                                                style: {
                                                                                    fontSize: 14,
                                                                                    color: '#52c41a'
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 202,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                style: {
                                                                                    color: '#595959',
                                                                                    fontSize: 14,
                                                                                    fontWeight: 500
                                                                                },
                                                                                children: userInfo.telephone
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 208,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 201,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 180,
                                                                columnNumber: 21
                                                            }, this),
                                                            userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 4,
                                                                align: "center",
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: '#8c8c8c',
                                                                        fontWeight: 500
                                                                    },
                                                                    children: [
                                                                        "📅 注册于 ",
                                                                        userInfo.registerDate
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 224,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 223,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 179,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 178,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginTop: 'auto'
                                                    },
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                        size: 16,
                                                        wrap: true,
                                                        direction: "vertical",
                                                        children: [
                                                            userInfo.lastLoginTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 4,
                                                                align: "center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#1890ff'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 244,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 13,
                                                                            color: '#8c8c8c',
                                                                            fontWeight: 500
                                                                        },
                                                                        children: [
                                                                            "最后登录：",
                                                                            userInfo.lastLoginTime
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 250,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 243,
                                                                columnNumber: 23
                                                            }, this),
                                                            userInfo.lastLoginTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 4,
                                                                align: "center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#52c41a'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 265,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 13,
                                                                            color: '#8c8c8c',
                                                                            fontWeight: 500
                                                                        },
                                                                        children: [
                                                                            "团队：",
                                                                            userInfo.lastLoginTeam
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 271,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 264,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 240,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 239,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 153,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 152,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        xs: 24,
                                        sm: 24,
                                        md: 10,
                                        lg: 10,
                                        xl: 10,
                                        children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                            message: "数据概览加载失败",
                                            description: statsError,
                                            type: "error",
                                            showIcon: true
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 290,
                                            columnNumber: 17
                                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                gutter: [
                                                    8,
                                                    8
                                                ],
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 12,
                                                        sm: 12,
                                                        md: 12,
                                                        lg: 12,
                                                        xl: 12,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center',
                                                                padding: '12px 8px',
                                                                background: '#f8f9fa',
                                                                borderRadius: 6
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 20,
                                                                        fontWeight: 700,
                                                                        color: '#1890ff',
                                                                        lineHeight: 1,
                                                                        marginBottom: 4
                                                                    },
                                                                    children: personalStats.vehicles
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 302,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 11,
                                                                        color: '#8c8c8c',
                                                                        fontWeight: 500
                                                                    },
                                                                    children: "车辆"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 313,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 301,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 300,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 12,
                                                        sm: 12,
                                                        md: 12,
                                                        lg: 12,
                                                        xl: 12,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center',
                                                                padding: '12px 8px',
                                                                background: '#f8f9fa',
                                                                borderRadius: 6
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 20,
                                                                        fontWeight: 700,
                                                                        color: '#52c41a',
                                                                        lineHeight: 1,
                                                                        marginBottom: 4
                                                                    },
                                                                    children: personalStats.personnel
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 328,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 11,
                                                                        color: '#8c8c8c',
                                                                        fontWeight: 500
                                                                    },
                                                                    children: "人员"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 339,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 327,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 326,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 12,
                                                        sm: 12,
                                                        md: 12,
                                                        lg: 12,
                                                        xl: 12,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center',
                                                                padding: '12px 8px',
                                                                background: '#f8f9fa',
                                                                borderRadius: 6
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 20,
                                                                        fontWeight: 700,
                                                                        color: '#faad14',
                                                                        lineHeight: 1,
                                                                        marginBottom: 4
                                                                    },
                                                                    children: personalStats.warnings
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 354,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 11,
                                                                        color: '#8c8c8c',
                                                                        fontWeight: 500
                                                                    },
                                                                    children: "预警"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 365,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 353,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 352,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 12,
                                                        sm: 12,
                                                        md: 12,
                                                        lg: 12,
                                                        xl: 12,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center',
                                                                padding: '12px 8px',
                                                                background: '#f8f9fa',
                                                                borderRadius: 6
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 20,
                                                                        fontWeight: 700,
                                                                        color: '#ff4d4f',
                                                                        lineHeight: 1,
                                                                        marginBottom: 4
                                                                    },
                                                                    children: personalStats.alerts
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 380,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 11,
                                                                        color: '#8c8c8c',
                                                                        fontWeight: 500
                                                                    },
                                                                    children: "告警"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 391,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 379,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 378,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 298,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 297,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 288,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                lineNumber: 150,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 148,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSettingsModal.default, {
                            visible: settingsModalVisible,
                            onCancel: ()=>setSettingsModalVisible(false),
                            userInfo: userInfo,
                            onSuccess: ()=>{
                                // 可以在这里刷新用户信息或团队列表
                                console.log('设置操作成功');
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 411,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                    lineNumber: 106,
                    columnNumber: 5
                }, this);
            };
            _s(PersonalInfo, "IzjvzsvZ1ZoM5sGldeIs9cfa/44=");
            _c = PersonalInfo;
            var _default = PersonalInfo;
            var _c;
            $RefreshReg$(_c, "PersonalInfo");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '9707774837104889534';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.17949435719099953889.hot-update.js.map